'use client'

import { useState, useEffect } from 'react'
import { Search, Filter, TrendingUp, Calendar, MapPin, DollarSign, Users, Zap, Bell, Settings, Target, BarChart3 } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { tenderApi, TenderRecord, TenderFilters, TenderStats } from '@/lib/supabase'
import { formatCurrency, formatNumber, getDaysUntilClosing, getUrgencyLevel } from '@/lib/utils'
import { TenderCard } from '@/components/TenderCard'
import { TenderModal } from '@/components/TenderModal'
import { StatsCard } from '@/components/StatsCard'
import { SearchFilters } from '@/components/SearchFilters'
import { AIRecommendations } from '@/components/AIRecommendations'
import { BEERequirementsCard } from '@/components/BEERequirementsCard'
import { SkillsSyncCard } from '@/components/SkillsSyncCard'
import { ToolsIntegrationCard } from '@/components/ToolsIntegrationCard'
import { enhanceTenderWithSampleData } from '@/lib/sample-data'
import { LoadingGrid } from '@/components/LoadingGrid'
import { BidPipeline } from '@/components/BidPipeline'
import { NotificationCenter } from '@/components/NotificationCenter'
import { QuickActions } from '@/components/QuickActions'
import { PsychologicalTriggerDisplay, usePsychologicalTriggers, GamificationProgress } from '@/components/PsychologicalTriggerDisplay'
import { GamificationDashboard } from '@/components/GamificationEngine'
import { SmartNotificationSystem } from '@/components/SmartNotificationSystem'
import { OnboardingFlow } from '@/components/OnboardingFlow'
import { BidWorkspace } from '@/components/BidWorkspace'
import { UserProfile, AIMatchingEngine, OpportunityMatch } from '@/lib/user-profile'

export default function Dashboard() {
  // State management
  const [tenders, setTenders] = useState<TenderRecord[]>([])
  const [stats, setStats] = useState<TenderStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTender, setSelectedTender] = useState<TenderRecord | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [activeTab, setActiveTab] = useState<'discover' | 'pipeline' | 'analytics'>('discover')
  const [filters, setFilters] = useState<TenderFilters>({
    search: '',
    status: 'Live/Open',  // Default to showing only live/open tenders
    sortBy: 'closing_date',  // Sort by closing date to show urgent ones first
    sortOrder: 'asc'
  })

  const itemsPerPage = 12
  const userId = 'user123' // In real app, this would come from auth

  // User profile and onboarding state
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [showBidWorkspace, setShowBidWorkspace] = useState(false)
  const [bidWorkspaceTender, setBidWorkspaceTender] = useState<TenderRecord | null>(null)
  const [selectedMatch, setSelectedMatch] = useState<OpportunityMatch | null>(null)

  // Psychological triggers integration
  const { triggerCount } = usePsychologicalTriggers(userId, 'dashboard_visit', {
    missedOpportunities: [],
    leaderboardPosition: 47
  })

  // Load tenders data
  const loadTenders = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, count, error } = await tenderApi.getTenders(filters, currentPage, itemsPerPage)
      
      if (error) {
        setError(error)
      } else {
        setTenders(data)
        setTotalCount(count)
      }
    } catch (err) {
      setError('Failed to load tenders')
      console.error('Error loading tenders:', err)
    } finally {
      setLoading(false)
    }
  }

  // Load statistics
  const loadStats = async () => {
    try {
      const { data } = await tenderApi.getTenderStats()
      setStats(data)
    } catch (err) {
      console.error('Error loading stats:', err)
    }
  }

  // Effects
  useEffect(() => {
    loadTenders()
  }, [filters, currentPage])

  useEffect(() => {
    loadStats()
  }, [])

  // Handle filter changes
  const handleFilterChange = (newFilters: TenderFilters) => {
    setFilters(newFilters)
    setCurrentPage(1)
  }

  // Onboarding completion handler
  const handleOnboardingComplete = (profile: UserProfile) => {
    setUserProfile(profile)
    setShowOnboarding(false)
    // Save profile to backend in real app
    console.log('Profile completed:', profile)
  }

  // Start bid handler - seamless transition to bid workspace
  const handleStartBid = (tender: TenderRecord, match: OpportunityMatch) => {
    if (!userProfile) {
      setShowOnboarding(true)
      return
    }

    setBidWorkspaceTender(tender)
    setSelectedMatch(match)
    setShowBidWorkspace(true)
  }

  // Bid submission handler
  const handleBidSubmit = (bidData: any) => {
    console.log('Bid submitted:', bidData)
    setShowBidWorkspace(false)
    setBidWorkspaceTender(null)
    setSelectedMatch(null)
    // Show success message and redirect
  }

  // Enhanced tender selection with AI matching
  const handleTenderSelectWithMatch = (tender: TenderRecord, match?: OpportunityMatch) => {
    setSelectedTender(tender)
    if (match) setSelectedMatch(match)
  }

  // Check if user needs onboarding
  useEffect(() => {
    // In real app, check if user profile exists
    const hasProfile = false // Replace with actual check
    if (!hasProfile) {
      setShowOnboarding(true)
    }
  }, [])

  // Handle tender selection
  const handleTenderSelect = (tender: TenderRecord) => {
    setSelectedTender(tender)
  }

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / itemsPerPage)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-lg">🐝</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">BYDER</h1>
                  <p className="text-xs text-gray-500">by BidBeez</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <SmartNotificationSystem />
              <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <Settings className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Gamification Progress */}
        <div className="mb-8">
          <GamificationProgress userId={userId} />
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <StatsCard
            title="Total Opportunities"
            value={formatNumber(stats?.total || 0)}
            icon={<TrendingUp className="w-5 h-5" />}
            trend="+12%"
            color="blue"
          />
          <StatsCard
            title="Live Opportunities"
            value="433"
            icon={<span className="text-lg">🔴</span>}
            trend="Open for Bidding"
            color="green"
          />
          <StatsCard
            title="RFQs Available"
            value="952"
            icon={<span className="text-lg">🍃</span>}
            trend="Low Hanging Fruits"
            color="green"
          />
          <StatsCard
            title="Total Value"
            value={formatCurrency(stats?.totalValue || 0)}
            icon={<DollarSign className="w-5 h-5" />}
            trend="+15%"
            color="purple"
          />
          <StatsCard
            title="Success Rate"
            value="94%"
            icon={<Zap className="w-5 h-5" />}
            trend="+3%"
            color="orange"
          />
        </div>

        {/* Main Navigation Tabs */}
        <div className="bg-white border border-gray-200 rounded-lg mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'discover', label: 'Discover Tenders', icon: Search },
                { id: 'pipeline', label: 'Bid Pipeline', icon: Target },
                { id: 'analytics', label: 'Analytics', icon: TrendingUp }
              ].map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'discover' && (
          <>
            {/* Search and Filters */}
            <div className="search-card mb-8">
              <SearchFilters
                filters={filters}
                onFiltersChange={handleFilterChange}
                loading={loading}
              />
            </div>

            {/* AI Recommendations */}
            <AIRecommendations
              userProfile={userProfile}
              onTenderSelect={handleTenderSelectWithMatch}
              onStartBid={handleStartBid}
              className="mb-8"
            />

            {/* Enhanced Tender Information Cards */}
            {selectedTender && (
              <div className="mb-8 space-y-6">
                <h2 className="text-xl font-bold text-gray-900">Detailed Information</h2>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <BEERequirementsCard tender={enhanceTenderWithSampleData(selectedTender)} />
                  <SkillsSyncCard tender={enhanceTenderWithSampleData(selectedTender)} />
                  <ToolsIntegrationCard tender={enhanceTenderWithSampleData(selectedTender)} />
                </div>
              </div>
            )}

            {/* Quick Access Cards for Featured Tenders */}
            {tenders.length > 0 && (
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900">Featured Opportunities</h2>
                  <span className="text-sm text-gray-500">Showing tenders with special requirements</span>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {tenders
                    .slice(0, 3) // Show first 3 tenders as featured (they'll have enhanced data)
                    .map(tender => {
                      const enhancedTender = enhanceTenderWithSampleData(tender)
                      return (
                        <div key={`featured-${tender.id}`} className="space-y-4">
                          <TenderCard
                            tender={tender} // TenderCard handles enhancement internally
                            onClick={() => handleTenderSelect(tender)}
                            className="mb-4"
                          />
                          <div className="grid grid-cols-1 gap-3">
                            <BEERequirementsCard tender={enhancedTender} className="text-xs" />
                            <SkillsSyncCard tender={enhancedTender} className="text-xs" />
                            <ToolsIntegrationCard tender={enhancedTender} className="text-xs" />
                          </div>
                        </div>
                      )
                    })
                  }
                </div>
              </div>
            )}

            {/* Results Section */}
            <div className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Tender Opportunities</h2>
                  <p className="text-gray-600">
                    Found {totalCount} opportunities matching your criteria
                  </p>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1 || loading}
                    className="px-4 py-2 bg-white/80 hover:bg-white border border-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Previous
                  </button>
                  <span className="px-4 py-2 text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages || loading}
                    className="px-4 py-2 bg-white/80 hover:bg-white border border-gray-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Next
                  </button>
                </div>
              </div>

              {/* Error State */}
              {error && (
                <div className="notification-error mb-6">
                  <p>Error: {error}</p>
                  <button
                    onClick={loadTenders}
                    className="mt-2 btn-primary"
                  >
                    Retry
                  </button>
                </div>
              )}

              {/* Loading State */}
              {loading && <LoadingGrid />}

              {/* Tender Grid */}
              {!loading && !error && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {tenders.map((tender) => (
                    <TenderCard
                      key={tender.id}
                      tender={tender}
                      onClick={() => handleTenderSelect(tender)}
                    />
                  ))}
                </div>
              )}

              {/* Empty State */}
              {!loading && !error && tenders.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Search className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No tenders found</h3>
                  <p className="text-gray-600 mb-4">Try adjusting your search criteria or filters</p>
                  <button
                    onClick={() => handleFilterChange({ search: '', sortBy: 'created_at', sortOrder: 'desc' })}
                    className="btn-primary"
                  >
                    Clear Filters
                  </button>
                </div>
              )}
            </div>
          </>
        )}

        {/* Bid Pipeline Tab */}
        {activeTab === 'pipeline' && (
          <BidPipeline />
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-6">
            {/* Gamification Dashboard */}
            <GamificationDashboard />

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Notifications */}
              <div className="lg:col-span-1">
                <NotificationCenter />
              </div>

              {/* Analytics Charts */}
              <div className="lg:col-span-2 space-y-6">
                <Card className="bg-white border border-gray-200">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <TrendingUp className="w-5 h-5 text-blue-600" />
                      <span>Bid Success Trends</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">This Month</span>
                        <span className="font-semibold text-green-600">+23%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Win Rate</span>
                        <span className="font-semibold text-blue-600">87%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Avg. Bid Value</span>
                        <span className="font-semibold text-purple-600">R2.1M</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Time to Submit</span>
                        <span className="font-semibold text-orange-600">12 days</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Actions FAB */}
      <QuickActions />

      {/* Psychological Triggers */}
      <PsychologicalTriggerDisplay
        userId={userId}
        context="dashboard_visit"
        data={{
          missedOpportunities: [],
          leaderboardPosition: 47,
          urgentTenders: tenders.filter(t => {
            const daysLeft = Math.ceil((new Date(t.closing_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
            return daysLeft <= 3 && daysLeft > 0
          })
        }}
      />

      {/* Tender Detail Modal */}
      {selectedTender && (
        <TenderModal
          tender={selectedTender}
          isOpen={!!selectedTender}
          onClose={() => setSelectedTender(null)}
        />
      )}

      {/* Onboarding Flow */}
      {showOnboarding && (
        <div className="fixed inset-0 z-50 bg-white">
          <OnboardingFlow
            onComplete={handleOnboardingComplete}
            onSkip={() => setShowOnboarding(false)}
          />
        </div>
      )}

      {/* Bid Workspace */}
      {showBidWorkspace && bidWorkspaceTender && selectedMatch && userProfile && (
        <div className="fixed inset-0 z-50 bg-white">
          <BidWorkspace
            tender={bidWorkspaceTender}
            userProfile={userProfile}
            opportunityMatch={selectedMatch}
            onBack={() => setShowBidWorkspace(false)}
            onSubmit={handleBidSubmit}
          />
        </div>
      )}
    </div>
  )
}
