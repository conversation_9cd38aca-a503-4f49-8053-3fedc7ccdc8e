import { TenderRecord } from './supabase'

// Sample data generators for demonstration when database fields are null
export const generateSampleSiteMeeting = (tender: TenderRecord) => {
  // Only generate if no existing site meeting data
  if (tender.site_meeting_required !== null) return null

  // Generate based on tender characteristics
  const isConstruction = tender.title?.toLowerCase().includes('construction') || 
                        tender.title?.toLowerCase().includes('building') ||
                        tender.main_procurement_category?.toLowerCase().includes('construction')
  
  const isHighValue = tender.tender_value && tender.tender_value > 1000000

  // 60% chance of site meeting for construction, 30% for high value, 20% for others
  const shouldHaveMeeting = isConstruction ? Math.random() < 0.6 : 
                           isHighValue ? Math.random() < 0.3 : 
                           Math.random() < 0.2

  if (!shouldHaveMeeting) return null

  const locations = [
    'Project Site Office',
    'Main Construction Site',
    'Client Premises',
    'Municipal Offices',
    'Proposed Development Site',
    'Existing Facility Location'
  ]

  const contacts = [
    { name: '<PERSON>', number: '+27 11 123 4567' },
    { name: '<PERSON>', number: '+27 21 987 6543' },
    { name: '<PERSON>', number: '+27 31 456 7890' },
    { name: '<PERSON>', number: '+27 12 345 6789' }
  ]

  const contact = contacts[Math.floor(Math.random() * contacts.length)]
  const location = locations[Math.floor(Math.random() * locations.length)]
  
  // Generate meeting date (usually 1-2 weeks before closing)
  const closingDate = tender.closing_date ? new Date(tender.closing_date) : new Date()
  const meetingDate = new Date(closingDate)
  meetingDate.setDate(meetingDate.getDate() - Math.floor(Math.random() * 14) - 7)

  return {
    site_meeting_required: true,
    site_meeting_date: meetingDate.toISOString(),
    site_meeting_time: ['09:00', '10:00', '14:00', '15:00'][Math.floor(Math.random() * 4)],
    site_meeting_location: location,
    site_meeting_contact_person: contact.name,
    site_meeting_contact_number: contact.number,
    site_meeting_mandatory: Math.random() < 0.7, // 70% chance of being mandatory
    site_meeting_registration_required: Math.random() < 0.4,
    site_meeting_parking_available: Math.random() < 0.8,
    site_meeting_security_requirements: isConstruction ? 'Safety boots and hard hat required' : null
  }
}

export const generateSampleBEERequirements = (tender: TenderRecord) => {
  // Only generate if no existing BEE data
  if (tender.bee_requirements !== null) return null

  // Higher chance for government tenders and high-value contracts
  const isGovernment = tender.issuer_type?.toLowerCase().includes('government') ||
                      tender.issuer_name?.toLowerCase().includes('municipality') ||
                      tender.issuer_name?.toLowerCase().includes('department')
  
  const isHighValue = tender.tender_value && tender.tender_value > 500000

  const shouldHaveBEE = isGovernment ? Math.random() < 0.9 : 
                       isHighValue ? Math.random() < 0.7 : 
                       Math.random() < 0.3

  if (!shouldHaveBEE) return null

  const levels = ['Level 1', 'Level 2', 'Level 3', 'Level 4']
  const level = levels[Math.floor(Math.random() * levels.length)]

  return {
    level_required: level,
    ownership_percentage: [51, 60, 75][Math.floor(Math.random() * 3)],
    women_participation: [25, 30, 35][Math.floor(Math.random() * 3)],
    youth_participation: [25, 30, 35][Math.floor(Math.random() * 3)],
    disabled_participation: [2, 5, 7][Math.floor(Math.random() * 3)],
    local_content_percentage: [60, 70, 75, 80][Math.floor(Math.random() * 4)],
    verification_required: true,
    certificate_validity_months: 12
  }
}

export const generateSampleSkills = (tender: TenderRecord) => {
  // Only generate if no existing skills data
  if (tender.skills_required !== null && tender.skills_required?.length > 0) return null

  const category = tender.main_procurement_category?.toLowerCase() || ''
  const title = tender.title?.toLowerCase() || ''

  let skillSets: string[][] = []

  if (category.includes('it') || title.includes('software') || title.includes('system')) {
    skillSets = [
      ['Software Development', 'Database Management', 'System Administration'],
      ['Network Security', 'Cloud Computing', 'DevOps'],
      ['Project Management', 'Technical Writing', 'Quality Assurance']
    ]
  } else if (category.includes('construction') || title.includes('building')) {
    skillSets = [
      ['Project Management', 'Construction Management', 'Safety Management'],
      ['Civil Engineering', 'Structural Engineering', 'Quantity Surveying'],
      ['Site Supervision', 'Quality Control', 'Health & Safety']
    ]
  } else if (category.includes('consulting') || title.includes('advisory')) {
    skillSets = [
      ['Business Analysis', 'Strategic Planning', 'Change Management'],
      ['Financial Analysis', 'Risk Management', 'Compliance'],
      ['Project Management', 'Stakeholder Management', 'Report Writing']
    ]
  } else {
    skillSets = [
      ['Project Management', 'Communication', 'Problem Solving'],
      ['Technical Writing', 'Data Analysis', 'Quality Assurance'],
      ['Customer Service', 'Team Leadership', 'Process Improvement']
    ]
  }

  const selectedSkillSet = skillSets[Math.floor(Math.random() * skillSets.length)]
  const levels = ['basic', 'intermediate', 'advanced', 'expert'] as const

  return selectedSkillSet.map(skill => ({
    skill,
    level: levels[Math.floor(Math.random() * levels.length)],
    mandatory: Math.random() < 0.6,
    years_experience: [2, 3, 5, 7, 10][Math.floor(Math.random() * 5)],
    certification_required: Math.random() < 0.3
  }))
}

export const generateSampleIntegrations = (tender: TenderRecord) => {
  // Generate integration status based on tender characteristics
  const isHighValue = tender.tender_value && tender.tender_value > 1000000
  const hasERPSync = Math.random() < (isHighValue ? 0.8 : 0.4)
  const hasCRMSync = Math.random() < 0.6
  const hasAccountingSync = Math.random() < (isHighValue ? 0.7 : 0.3)

  const statuses = ['synced', 'pending', 'failed', 'disabled'] as const
  
  return {
    erp_sync_status: hasERPSync ? statuses[Math.floor(Math.random() * statuses.length)] : null,
    erp_project_id: hasERPSync ? `ERP-${Math.floor(Math.random() * 10000)}` : null,
    crm_opportunity_id: hasCRMSync ? `CRM-${Math.floor(Math.random() * 10000)}` : null,
    accounting_sync_status: hasAccountingSync ? statuses[Math.floor(Math.random() * statuses.length)] : null,
    external_system_refs: hasERPSync || hasCRMSync || hasAccountingSync ? {
      erp: hasERPSync ? {
        id: `ERP-${Math.floor(Math.random() * 10000)}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        last_sync: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      } : undefined,
      crm: hasCRMSync ? {
        id: `CRM-${Math.floor(Math.random() * 10000)}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        last_sync: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      } : undefined,
      accounting: hasAccountingSync ? {
        id: `ACC-${Math.floor(Math.random() * 10000)}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        last_sync: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      } : undefined
    } : null
  }
}

// Enhanced tender with sample data
export const enhanceTenderWithSampleData = (tender: TenderRecord): TenderRecord => {
  const siteMeeting = generateSampleSiteMeeting(tender)
  const beeRequirements = generateSampleBEERequirements(tender)
  const skills = generateSampleSkills(tender)
  const integrations = generateSampleIntegrations(tender)

  return {
    ...tender,
    ...siteMeeting,
    bee_requirements: beeRequirements,
    skills_required: skills,
    skillsync_integration_enabled: skills ? Math.random() < 0.4 : false,
    skillsync_job_categories: skills ? [
      { category: 'Professional Services', priority: 'high' as const },
      { category: 'Technical Services', priority: 'medium' as const }
    ] : null,
    ...integrations
  }
}
