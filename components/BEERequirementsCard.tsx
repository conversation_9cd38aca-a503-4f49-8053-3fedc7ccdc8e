'use client'

import { Shield, Users, Award, CheckCircle, AlertCircle, TrendingUp, Target, Play, Pause, RotateCcw, Clock, Zap } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { TenderRecord } from '@/lib/supabase'
import { generateSampleBEERequirements } from '@/lib/sample-data'
import { cn } from '@/lib/utils'
import { useState } from 'react'

interface BEETaskRunnersCardProps {
  tender: TenderRecord
  className?: string
}

export function BEERequirementsCard({ tender, className }: BEETaskRunnersCardProps) {
  const [taskStatuses, setTaskStatuses] = useState<Record<string, 'pending' | 'running' | 'completed' | 'failed'>>({})
  const [isRunningAll, setIsRunningAll] = useState(false)

  // Use existing data or generate sample data
  const beeReqs = tender.bee_requirements || generateSampleBEERequirements(tender)

  if (!beeReqs) {
    return (
      <Card className={cn("hover:shadow-md transition-shadow bg-gray-50", className)}>
        <CardContent className="p-4 text-center">
          <Shield className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No BEE requirements for this tender</p>
        </CardContent>
      </Card>
    )
  }

  const getBEELevelColor = (level?: string) => {
    if (!level) return 'bg-gray-100 text-gray-700'
    const levelNum = parseInt(level.replace(/\D/g, ''))
    if (levelNum <= 2) return 'bg-green-100 text-green-700 border-green-200'
    if (levelNum <= 4) return 'bg-yellow-100 text-yellow-700 border-yellow-200'
    return 'bg-red-100 text-red-700 border-red-200'
  }

  const getParticipationStatus = (required: number, type: string) => {
    if (required >= 30) return { status: 'high', color: 'text-green-600', icon: CheckCircle }
    if (required >= 15) return { status: 'medium', color: 'text-yellow-600', icon: AlertCircle }
    return { status: 'low', color: 'text-red-600', icon: AlertCircle }
  }

  // BEE Task Management Functions
  const runTask = (taskId: string) => {
    setTaskStatuses(prev => ({ ...prev, [taskId]: 'running' }))

    // Simulate task execution
    setTimeout(() => {
      const success = Math.random() > 0.2 // 80% success rate
      setTaskStatuses(prev => ({
        ...prev,
        [taskId]: success ? 'completed' : 'failed'
      }))
    }, 2000 + Math.random() * 3000) // 2-5 seconds
  }

  const runAllTasks = () => {
    setIsRunningAll(true)
    const tasks = ['verification', 'ownership', 'participation', 'local_content']

    tasks.forEach((task, index) => {
      setTimeout(() => runTask(task), index * 1000)
    })

    setTimeout(() => setIsRunningAll(false), tasks.length * 1000 + 5000)
  }

  const getTaskStatus = (taskId: string) => {
    return taskStatuses[taskId] || 'pending'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <RotateCcw className="w-4 h-4 animate-spin text-blue-600" />
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-600" />
      default: return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <Card className={cn("hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <Shield className="w-5 h-5 text-blue-600" />
            <span>BEE Task Runners</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            {beeReqs.level_required && (
              <Badge className={cn("font-medium", getBEELevelColor(beeReqs.level_required))}>
                {beeReqs.level_required}
              </Badge>
            )}
            <Button
              size="sm"
              onClick={runAllTasks}
              disabled={isRunningAll}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isRunningAll ? (
                <>
                  <RotateCcw className="w-3 h-3 mr-1 animate-spin" />
                  Running...
                </>
              ) : (
                <>
                  <Play className="w-3 h-3 mr-1" />
                  Run All
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* BEE Verification Task */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <Shield className="w-4 h-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">BEE Certificate Verification</span>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusIcon(getTaskStatus('verification'))}
              <Button
                size="sm"
                variant="outline"
                onClick={() => runTask('verification')}
                disabled={getTaskStatus('verification') === 'running'}
                className="h-6 px-2 text-xs"
              >
                {getTaskStatus('verification') === 'running' ? 'Running' : 'Run'}
              </Button>
            </div>
          </div>
          <p className="text-xs text-yellow-600">Verify BEE certificate validity and compliance</p>
        </div>

        {/* Ownership Requirements Task */}
        {beeReqs.ownership_percentage && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Award className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Ownership Verification</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-bold text-blue-700">
                  {beeReqs.ownership_percentage}%
                </span>
                {getStatusIcon(getTaskStatus('ownership'))}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => runTask('ownership')}
                  disabled={getTaskStatus('ownership') === 'running'}
                  className="h-6 px-2 text-xs"
                >
                  {getTaskStatus('ownership') === 'running' ? 'Running' : 'Run'}
                </Button>
              </div>
            </div>
            <p className="text-xs text-blue-600">Verify minimum ownership requirement compliance</p>
          </div>
        )}

        {/* Participation Requirements Task */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Participation Verification</span>
            </div>
            <div className="flex items-center space-x-2">
              {getStatusIcon(getTaskStatus('participation'))}
              <Button
                size="sm"
                variant="outline"
                onClick={() => runTask('participation')}
                disabled={getTaskStatus('participation') === 'running'}
                className="h-6 px-2 text-xs"
              >
                {getTaskStatus('participation') === 'running' ? 'Running' : 'Run'}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-2">
            {/* Women Participation */}
            {beeReqs.women_participation && (
              <div className="flex items-center justify-between p-2 bg-pink-50 rounded-md">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                  <span className="text-xs text-pink-800">Women</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs font-semibold text-pink-700">
                    {beeReqs.women_participation}%
                  </span>
                  {(() => {
                    const status = getParticipationStatus(beeReqs.women_participation, 'women')
                    const Icon = status.icon
                    return <Icon className={cn("w-3 h-3", status.color)} />
                  })()}
                </div>
              </div>
            )}

            {/* Youth Participation */}
            {beeReqs.youth_participation && (
              <div className="flex items-center justify-between p-2 bg-green-50 rounded-md">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-800">Youth</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-semibold text-green-700">
                    {beeReqs.youth_participation}%
                  </span>
                  {(() => {
                    const status = getParticipationStatus(beeReqs.youth_participation, 'youth')
                    const Icon = status.icon
                    return <Icon className={cn("w-3 h-3", status.color)} />
                  })()}
                </div>
              </div>
            )}

            {/* Disabled Participation */}
            {beeReqs.disabled_participation && (
              <div className="flex items-center justify-between p-2 bg-purple-50 rounded-md">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-purple-800">Disabled</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-semibold text-purple-700">
                    {beeReqs.disabled_participation}%
                  </span>
                  {(() => {
                    const status = getParticipationStatus(beeReqs.disabled_participation, 'disabled')
                    const Icon = status.icon
                    return <Icon className={cn("w-3 h-3", status.color)} />
                  })()}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Local Content */}
        {beeReqs.local_content_percentage && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Target className="w-4 h-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">Local Content</span>
              </div>
              <span className="text-lg font-bold text-orange-700">
                {beeReqs.local_content_percentage}%
              </span>
            </div>
            <p className="text-xs text-orange-600">Minimum local content requirement</p>
          </div>
        )}

        {/* Verification Status */}
        {beeReqs.verification_required && (
          <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
            <CheckCircle className="w-4 h-4 text-gray-600" />
            <span className="text-sm text-gray-700">BEE Certificate Verification Required</span>
            {beeReqs.certificate_validity_months && (
              <Badge variant="outline" className="text-xs">
                Valid for {beeReqs.certificate_validity_months} months
              </Badge>
            )}
          </div>
        )}

        {/* Subcontracting Requirements */}
        {beeReqs.subcontracting_requirements && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <h5 className="text-sm font-medium text-gray-700 mb-1">Subcontracting</h5>
            <p className="text-xs text-gray-600">{beeReqs.subcontracting_requirements}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
