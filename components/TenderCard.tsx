'use client'

import { Calendar, MapPin, DollarSign, Building, Clock, Zap, Star, Eye, Users, TrendingUp, MapPinIcon, Phone, AlertTriangle, CheckCircle } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { TenderRecord } from '@/lib/supabase'
import { generateSampleSiteMeeting, enhanceTenderWithSampleData } from '@/lib/sample-data'
import {
  formatCurrency,
  formatDate,
  getDaysUntilClosing,
  getUrgencyLevel,
  getUrgencyColor,
  truncateText,
  calculateSuccessProbability,
  generateAIInsights
} from '@/lib/utils'
import { cn } from '@/lib/utils'

interface TenderCardProps {
  tender: TenderRecord
  onClick: () => void
  className?: string
}

export function TenderCard({ tender, onClick, className }: TenderCardProps) {
  // Enhance tender with sample data if needed
  const enhancedTender = enhanceTenderWithSampleData(tender)

  const daysUntilClosing = getDaysUntilClosing(enhancedTender.closing_date)
  const urgency = getUrgencyLevel(enhancedTender.closing_date)
  const urgencyColor = getUrgencyColor(urgency)
  const successProbability = calculateSuccessProbability(enhancedTender)
  const aiInsights = generateAIInsights(enhancedTender)

  // Psychological triggers - simulate real-time data
  const viewCount = Math.floor(Math.random() * 25) + 5
  const competitorCount = Math.floor(Math.random() * 15) + 3
  const isHot = viewCount > 15
  const isCompetitive = competitorCount > 8

  const getStatusBadge = () => {
    if (daysUntilClosing === null) return null
    
    if (daysUntilClosing < 0) {
      return <Badge variant="destructive">⚫ Closed</Badge>
    } else if (daysUntilClosing <= 2) {
      return <Badge variant="destructive">🔥 Urgent - {daysUntilClosing}d left</Badge>
    } else if (daysUntilClosing <= 7) {
      return <Badge variant="warning">🟡 Closing Soon - {daysUntilClosing}d left</Badge>
    } else {
      return <Badge variant="success">🔴 Live - Can Bid</Badge>
    }
  }

  const getUrgencyBadge = () => {
    if (daysUntilClosing === null || daysUntilClosing < 0) return null
    
    return (
      <Badge 
        variant={urgencyColor === 'red' ? 'destructive' : urgencyColor === 'orange' ? 'warning' : 'info'}
        className="text-xs"
      >
        {daysUntilClosing === 0 ? 'Today' : `${daysUntilClosing}d left`}
      </Badge>
    )
  }

  return (
    <Card
      className={cn(
        'tender-card',
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-4">
        {/* Psychological Trigger Indicators */}
          <div className="mb-3 space-y-2">
            {/* Opportunity Type Badge */}
            <div className="flex items-center space-x-2">
              {tender.release_type === 'quotation' && (
                <>
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    🍃 RFQ - Low Hanging Fruit
                  </Badge>
                  <span className="text-xs text-green-600 font-medium">Quick Win</span>
                </>
              )}
              {tender.release_type === 'open_tender' && (
                <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                  📋 Formal Tender
                </Badge>
              )}
              {tender.release_type === 'direct_appointment' && (
                <Badge className="bg-purple-100 text-purple-700 border-purple-200">
                  🎯 Direct Appointment
                </Badge>
              )}
            </div>

            {/* Social Proof & Scarcity Indicators */}
            <div className="flex items-center space-x-3 text-xs">
              {isHot && (
                <div className="flex items-center space-x-1 text-red-600 animate-pulse">
                  <TrendingUp className="w-3 h-3" />
                  <span className="font-medium">HOT</span>
                </div>
              )}

              <div className="flex items-center space-x-1 text-gray-600">
                <Eye className="w-3 h-3" />
                <span>{viewCount} viewing</span>
              </div>

              {isCompetitive && (
                <div className="flex items-center space-x-1 text-orange-600">
                  <Users className="w-3 h-3" />
                  <span className="font-medium">{competitorCount} competing</span>
                </div>
              )}
            </div>
          </div>

        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            {getStatusBadge()}
            {getUrgencyBadge()}
          </div>

          <div className="flex items-center space-x-1 bg-yellow-50 px-2 py-1 rounded-md">
            <Star className="w-3 h-3 text-yellow-500 fill-current" />
            <span className="text-xs font-medium text-yellow-700">
              {successProbability}%
            </span>
          </div>
        </div>

        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
          {tender.title || 'Untitled Tender'}
        </h3>

        <p className="text-sm text-gray-600 line-clamp-2">
          {truncateText(tender.description, 120) || 'No description available'}
        </p>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Issuer */}
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Building className="w-4 h-4 flex-shrink-0 text-gray-400" />
            <span className="truncate">
              {tender.issuer_name || 'Unknown Issuer'}
            </span>
          </div>

          {/* Location */}
          {tender.province && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MapPin className="w-4 h-4 flex-shrink-0 text-gray-400" />
              <span>{tender.province}</span>
            </div>
          )}

          {/* Value */}
          {tender.tender_value && (
            <div className="flex items-center space-x-2 text-sm">
              <DollarSign className="w-4 h-4 flex-shrink-0 text-gray-400" />
              <span className="font-semibold text-green-600">
                {formatCurrency(tender.tender_value, tender.currency)}
              </span>
            </div>
          )}

          {/* Closing Date */}
          {tender.closing_date && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Calendar className="w-4 h-4 flex-shrink-0 text-gray-400" />
              <span>Closes: {formatDate(tender.closing_date)}</span>
            </div>
          )}

          {/* Site Meeting Details - Always show if available */}
          {enhancedTender.site_meeting_required && (
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 space-y-2">
              <div className="flex items-center space-x-2">
                <MapPinIcon className="w-4 h-4 text-amber-600" />
                <span className="text-sm font-medium text-amber-800">
                  Site Meeting {enhancedTender.site_meeting_mandatory ? 'Required' : 'Optional'}
                </span>
                {enhancedTender.site_meeting_mandatory && (
                  <AlertTriangle className="w-3 h-3 text-red-500" />
                )}
              </div>

              {enhancedTender.site_meeting_date && (
                <div className="flex items-center space-x-2 text-xs text-amber-700">
                  <Calendar className="w-3 h-3" />
                  <span>{formatDate(enhancedTender.site_meeting_date)}</span>
                  {enhancedTender.site_meeting_time && (
                    <span>at {enhancedTender.site_meeting_time}</span>
                  )}
                </div>
              )}

              {enhancedTender.site_meeting_location && (
                <div className="flex items-center space-x-2 text-xs text-amber-700">
                  <MapPin className="w-3 h-3" />
                  <span className="truncate">{enhancedTender.site_meeting_location}</span>
                </div>
              )}

              {enhancedTender.site_meeting_contact_person && (
                <div className="flex items-center space-x-2 text-xs text-amber-700">
                  <Phone className="w-3 h-3" />
                  <span>{enhancedTender.site_meeting_contact_person}</span>
                  {enhancedTender.site_meeting_contact_number && (
                    <span>({enhancedTender.site_meeting_contact_number})</span>
                  )}
                </div>
              )}

              {enhancedTender.site_meeting_registration_required && (
                <div className="text-xs text-amber-600 font-medium">
                  Registration Required
                  {enhancedTender.site_meeting_registration_deadline && (
                    <span className="text-amber-500">
                      {' '}by {formatDate(enhancedTender.site_meeting_registration_deadline)}
                    </span>
                  )}
                </div>
              )}

              {enhancedTender.site_meeting_security_requirements && (
                <div className="text-xs text-amber-600 bg-amber-100 p-1 rounded">
                  <AlertTriangle className="w-3 h-3 inline mr-1" />
                  {enhancedTender.site_meeting_security_requirements}
                </div>
              )}
            </div>
          )}

          {/* No Site Meeting Indicator */}
          {!enhancedTender.site_meeting_required && (
            <div className="bg-green-50 border border-green-200 rounded-md p-2">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-3 h-3 text-green-600" />
                <span className="text-xs text-green-700">No site meeting required</span>
              </div>
            </div>
          )}

          {/* AI Insights Preview */}
          <div className="pt-3 border-t border-gray-100">
            <div className="flex items-center space-x-1 mb-2">
              <Zap className="w-3 h-3 text-blue-500" />
              <span className="text-xs font-medium text-blue-600">AI Insights</span>
            </div>
            <div className="space-y-1">
              {aiInsights.slice(0, 2).map((insight, index) => (
                <div key={index} className="flex items-start space-x-2">
                  <div className="w-1 h-1 bg-blue-500 rounded-full flex-shrink-0 mt-2" />
                  <span className="text-xs text-gray-600 leading-relaxed">{insight}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log('Add to pipeline:', tender.id)
                  }}
                  className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                >
                  + Pipeline
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log('Estimate costs:', tender.id)
                  }}
                  className="text-xs text-green-600 hover:text-green-800 font-medium"
                >
                  💰 Estimate
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    console.log('Assign team:', tender.id)
                  }}
                  className="text-xs text-purple-600 hover:text-purple-800 font-medium"
                >
                  👥 Team
                </button>
              </div>

              {tender.tender_number && (
                <span className="text-xs text-gray-500">
                  {tender.tender_number}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
