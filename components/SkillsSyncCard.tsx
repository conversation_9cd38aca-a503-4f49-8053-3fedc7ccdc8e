'use client'

import { Brain, Zap, CheckCircle, AlertCircle, Clock, <PERSON>, <PERSON>, Sync, Target, Award, Play, RefreshCw, Database, Link } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { TenderRecord } from '@/lib/supabase'
import { generateSampleSkills } from '@/lib/sample-data'
import { cn } from '@/lib/utils'
import { useState } from 'react'

interface SkillsSyncCardProps {
  tender: TenderRecord
  className?: string
}

export function SkillsSyncCard({ tender, className }: SkillsSyncCardProps) {
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'synced' | 'error'>('idle')
  const [matchedCandidates, setMatchedCandidates] = useState<number>(0)

  // Use existing data or generate sample data
  const skills = tender.skills_required || generateSampleSkills(tender)
  const hasPreferredSkills = tender.skills_preferred && tender.skills_preferred.length > 0
  const hasSkillSync = tender.skillsync_integration_enabled || Math.random() < 0.6 // 60% chance if not set

  if (!skills && !hasPreferredSkills && !hasSkillSync) {
    return (
      <Card className={cn("hover:shadow-md transition-shadow bg-gray-50", className)}>
        <CardContent className="p-4 text-center">
          <Brain className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No skills requirements for this tender</p>
        </CardContent>
      </Card>
    )
  }

  const performSkillSync = async () => {
    setSyncStatus('syncing')

    // Simulate API call to SkillSync platform
    setTimeout(() => {
      const success = Math.random() > 0.1 // 90% success rate
      if (success) {
        setSyncStatus('synced')
        setMatchedCandidates(Math.floor(Math.random() * 50) + 5) // 5-55 candidates
      } else {
        setSyncStatus('error')
      }
    }, 3000) // 3 second sync simulation
  }

  const getSkillLevelColor = (level: string) => {
    switch (level) {
      case 'basic': return 'bg-green-100 text-green-700 border-green-200'
      case 'intermediate': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'advanced': return 'bg-orange-100 text-orange-700 border-orange-200'
      case 'expert': return 'bg-red-100 text-red-700 border-red-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getSkillLevelIcon = (level: string) => {
    switch (level) {
      case 'basic': return '●'
      case 'intermediate': return '●●'
      case 'advanced': return '●●●'
      case 'expert': return '●●●●'
      default: return '●'
    }
  }

  const getSyncStatusIcon = () => {
    switch (syncStatus) {
      case 'syncing': return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />
      case 'synced': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'error': return <AlertCircle className="w-4 h-4 text-red-500" />
      default: return <Database className="w-4 h-4 text-gray-400" />
    }
  }

  const getSyncStatusText = () => {
    switch (syncStatus) {
      case 'syncing': return 'Syncing...'
      case 'synced': return `${matchedCandidates} matches found`
      case 'error': return 'Sync failed'
      default: return 'Ready to sync'
    }
  }

  return (
    <Card className={cn("hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <Brain className="w-5 h-5 text-purple-600" />
            <span>Skills Sync</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            {hasSkillSync && (
              <div className="flex items-center space-x-1">
                <Sync className="w-4 h-4 text-blue-500" />
                <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                  SkillSync Enabled
                </Badge>
              </div>
            )}
            <Button
              size="sm"
              onClick={performSkillSync}
              disabled={syncStatus === 'syncing'}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {syncStatus === 'syncing' ? (
                <>
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <Play className="w-3 h-3 mr-1" />
                  Sync Now
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Sync Status Bar */}
        <div className="flex items-center space-x-2 mt-2 p-2 bg-gray-50 rounded-md">
          {getSyncStatusIcon()}
          <span className="text-sm text-gray-700">{getSyncStatusText()}</span>
          {syncStatus === 'synced' && (
            <Badge variant="outline" className="ml-auto">
              <Link className="w-3 h-3 mr-1" />
              View Matches
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Required Skills */}
        {skills && skills.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Target className="w-4 h-4 text-red-600" />
              <h4 className="text-sm font-medium text-gray-700">Required Skills</h4>
              <Badge variant="outline" className="text-xs">
                {skills.length} skills
              </Badge>
            </div>

            <div className="space-y-2">
              {skills.slice(0, 5).map((skill, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      {skill.mandatory && (
                        <AlertCircle className="w-3 h-3 text-red-500" />
                      )}
                      <span className="text-sm font-medium text-gray-800">
                        {skill.skill}
                      </span>
                    </div>
                    {skill.years_experience && (
                      <Badge variant="outline" className="text-xs">
                        {skill.years_experience}+ years
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge className={cn("text-xs font-mono", getSkillLevelColor(skill.level))}>
                      {getSkillLevelIcon(skill.level)} {skill.level}
                    </Badge>
                    {skill.certification_required && (
                      <Award className="w-3 h-3 text-yellow-600" />
                    )}
                  </div>
                </div>
              ))}
              
              {skills.length > 5 && (
                <div className="text-center">
                  <Badge variant="outline" className="text-xs">
                    +{skills.length - 5} more skills
                  </Badge>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Preferred Skills */}
        {hasPreferredSkills && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-yellow-600" />
              <h4 className="text-sm font-medium text-gray-700">Preferred Skills</h4>
              <Badge variant="outline" className="text-xs">
                {tender.skills_preferred!.length} skills
              </Badge>
            </div>
            
            <div className="space-y-2">
              {tender.skills_preferred!.slice(0, 3).map((skill, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-yellow-50 rounded-md">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-yellow-800">{skill.skill}</span>
                    {skill.years_experience && (
                      <Badge variant="outline" className="text-xs">
                        {skill.years_experience}+ years
                      </Badge>
                    )}
                  </div>
                  <Badge className={cn("text-xs font-mono", getSkillLevelColor(skill.level))}>
                    {getSkillLevelIcon(skill.level)} {skill.level}
                  </Badge>
                </div>
              ))}
              
              {tender.skills_preferred!.length > 3 && (
                <div className="text-center">
                  <Badge variant="outline" className="text-xs">
                    +{tender.skills_preferred!.length - 3} more preferred
                  </Badge>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Experience Requirements */}
        {tender.minimum_experience_years && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Minimum Experience</span>
              </div>
              <span className="text-lg font-bold text-blue-700">
                {tender.minimum_experience_years} years
              </span>
            </div>
          </div>
        )}

        {/* Team Composition */}
        {tender.team_composition_required && tender.team_composition_required.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-green-600" />
              <h4 className="text-sm font-medium text-gray-700">Team Composition</h4>
            </div>
            
            <div className="space-y-2">
              {tender.team_composition_required.slice(0, 3).map((role, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded-md">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-green-800">{role.role}</span>
                    <Badge variant="outline" className="text-xs">
                      {role.count} person{role.count > 1 ? 's' : ''}
                    </Badge>
                  </div>
                  <div className="text-xs text-green-600">
                    {role.experience_years}+ years exp
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* SkillSync Integration Details */}
        {hasSkillSync && tender.skillsync_matching_criteria && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <Sync className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">SkillSync Matching</span>
            </div>
            
            <div className="space-y-1 text-xs text-blue-700">
              {tender.skillsync_matching_criteria.minimum_match_percentage && (
                <div>Min Match: {tender.skillsync_matching_criteria.minimum_match_percentage}%</div>
              )}
              {tender.skillsync_matching_criteria.preferred_location_radius && (
                <div>Location: {tender.skillsync_matching_criteria.preferred_location_radius}km radius</div>
              )}
              {tender.skillsync_matching_criteria.rate_range && (
                <div>
                  Rate: {tender.skillsync_matching_criteria.rate_range.currency} {tender.skillsync_matching_criteria.rate_range.min} - {tender.skillsync_matching_criteria.rate_range.max}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Job Categories */}
        {tender.skillsync_job_categories && tender.skillsync_job_categories.length > 0 && (
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-700">Job Categories</h5>
            <div className="flex flex-wrap gap-1">
              {tender.skillsync_job_categories.slice(0, 4).map((category, index) => (
                <Badge 
                  key={index} 
                  variant="outline" 
                  className={cn(
                    "text-xs",
                    category.priority === 'high' ? 'border-red-300 text-red-700' :
                    category.priority === 'medium' ? 'border-yellow-300 text-yellow-700' :
                    'border-green-300 text-green-700'
                  )}
                >
                  {category.category}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
