'use client'

import { <PERSON><PERSON><PERSON>, CheckCircle, AlertCircle, XCircle, Clock, Zap, Database, CreditCard, Building, Sync, RefreshCw, ExternalLink, Play, Pause } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { TenderRecord } from '@/lib/supabase'
import { generateSampleIntegrations } from '@/lib/sample-data'
import { cn } from '@/lib/utils'
import { useState } from 'react'

interface ToolsIntegrationCardProps {
  tender: TenderRecord
  className?: string
}

export function ToolsIntegrationCard({ tender, className }: ToolsIntegrationCardProps) {
  const [syncingStates, setSyncingStates] = useState<Record<string, boolean>>({})

  // Use existing data or generate sample data
  const sampleIntegrations = generateSampleIntegrations(tender)
  const integrationData = {
    erp_sync_status: tender.erp_sync_status || sampleIntegrations.erp_sync_status,
    erp_project_id: tender.erp_project_id || sampleIntegrations.erp_project_id,
    crm_opportunity_id: tender.crm_opportunity_id || sampleIntegrations.crm_opportunity_id,
    accounting_sync_status: tender.accounting_sync_status || sampleIntegrations.accounting_sync_status,
    external_system_refs: tender.external_system_refs || sampleIntegrations.external_system_refs
  }

  const hasIntegrations = integrationData.erp_sync_status || integrationData.crm_opportunity_id || integrationData.accounting_sync_status || integrationData.external_system_refs

  if (!hasIntegrations) {
    return (
      <Card className={cn("hover:shadow-md transition-shadow bg-gray-50", className)}>
        <CardContent className="p-4 text-center">
          <Settings className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No system integrations configured</p>
          <Button size="sm" className="mt-2" variant="outline">
            <Zap className="w-3 h-3 mr-1" />
            Setup Integrations
          </Button>
        </CardContent>
      </Card>
    )
  }

  const performSync = (system: string) => {
    setSyncingStates(prev => ({ ...prev, [system]: true }))

    // Simulate sync operation
    setTimeout(() => {
      setSyncingStates(prev => ({ ...prev, [system]: false }))
      // In a real app, this would update the sync status
    }, 2000 + Math.random() * 3000) // 2-5 seconds
  }

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'synced': return { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100' }
      case 'pending': return { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-100' }
      case 'failed': return { icon: XCircle, color: 'text-red-600', bg: 'bg-red-100' }
      case 'disabled': return { icon: AlertCircle, color: 'text-gray-600', bg: 'bg-gray-100' }
      default: return { icon: AlertCircle, color: 'text-gray-400', bg: 'bg-gray-50' }
    }
  }

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'synced': return 'Synced'
      case 'pending': return 'Pending'
      case 'failed': return 'Failed'
      case 'disabled': return 'Disabled'
      default: return 'Unknown'
    }
  }

  const formatLastSync = (timestamp?: string) => {
    if (!timestamp) return 'Never'
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return `${diffDays}d ago`
  }

  return (
    <Card className={cn("hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold flex items-center space-x-2">
            <Settings className="w-5 h-5 text-gray-600" />
            <span>Tools Sync</span>
          </CardTitle>
          <Button
            size="sm"
            onClick={() => {
              performSync('erp')
              performSync('crm')
              performSync('accounting')
            }}
            className="bg-gray-600 hover:bg-gray-700"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Sync All
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* ERP Integration */}
        {integrationData.erp_sync_status && (
          <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-3">
              <Database className="w-5 h-5 text-blue-600" />
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-blue-800">ERP System</span>
                  {integrationData.erp_project_id && (
                    <Badge variant="outline" className="text-xs">
                      ID: {integrationData.erp_project_id}
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-blue-600">Enterprise Resource Planning</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {syncingStates.erp ? (
                <div className="p-1 rounded-full bg-blue-100">
                  <RefreshCw className="w-4 h-4 animate-spin text-blue-600" />
                </div>
              ) : (
                (() => {
                  const status = getStatusIcon(integrationData.erp_sync_status)
                  const Icon = status.icon
                  return (
                    <div className={cn("p-1 rounded-full", status.bg)}>
                      <Icon className={cn("w-4 h-4", status.color)} />
                    </div>
                  )
                })()
              )}
              <div className="text-right">
                <div className="text-sm font-medium text-gray-800">
                  {syncingStates.erp ? 'Syncing...' : getStatusText(integrationData.erp_sync_status)}
                </div>
                <div className="text-xs text-gray-500">
                  Last sync: {formatLastSync(integrationData.external_system_refs?.erp?.last_sync)}
                </div>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={() => performSync('erp')}
                disabled={syncingStates.erp}
                className="h-6 px-2 text-xs"
              >
                {syncingStates.erp ? 'Syncing' : 'Sync'}
              </Button>
            </div>
          </div>
        )}

        {/* CRM Integration */}
        {tender.crm_opportunity_id && (
          <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-3">
              <Building className="w-5 h-5 text-green-600" />
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-green-800">CRM System</span>
                  <Badge variant="outline" className="text-xs">
                    ID: {tender.crm_opportunity_id}
                  </Badge>
                </div>
                <p className="text-xs text-green-600">Customer Relationship Management</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="p-1 rounded-full bg-green-100">
                <CheckCircle className="w-4 h-4 text-green-600" />
              </div>
              <div className="text-right">
                <div className="text-sm font-medium text-gray-800">Connected</div>
                <div className="text-xs text-gray-500">
                  Last sync: {formatLastSync(tender.external_system_refs?.crm?.last_sync)}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Accounting Integration */}
        {tender.accounting_sync_status && (
          <div className="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg">
            <div className="flex items-center space-x-3">
              <CreditCard className="w-5 h-5 text-purple-600" />
              <div>
                <span className="text-sm font-medium text-purple-800">Accounting System</span>
                <p className="text-xs text-purple-600">Financial Management</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {(() => {
                const status = getStatusIcon(tender.accounting_sync_status)
                const Icon = status.icon
                return (
                  <div className={cn("p-1 rounded-full", status.bg)}>
                    <Icon className={cn("w-4 h-4", status.color)} />
                  </div>
                )
              })()}
              <div className="text-right">
                <div className="text-sm font-medium text-gray-800">
                  {getStatusText(tender.accounting_sync_status)}
                </div>
                <div className="text-xs text-gray-500">
                  Last sync: {formatLastSync(tender.external_system_refs?.accounting?.last_sync)}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* External Systems */}
        {tender.external_system_refs && Object.keys(tender.external_system_refs).length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 flex items-center space-x-2">
              <ExternalLink className="w-4 h-4" />
              <span>External Systems</span>
            </h4>
            
            <div className="space-y-2">
              {Object.entries(tender.external_system_refs).map(([system, details]) => (
                <div key={system} className="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                  <div className="flex items-center space-x-2">
                    <Sync className="w-4 h-4 text-gray-600" />
                    <div>
                      <span className="text-sm font-medium text-gray-800 capitalize">
                        {system}
                      </span>
                      {details.id && (
                        <div className="text-xs text-gray-500">ID: {details.id}</div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {(() => {
                      const status = getStatusIcon(details.status)
                      const Icon = status.icon
                      return (
                        <div className={cn("p-1 rounded-full", status.bg)}>
                          <Icon className={cn("w-3 h-3", status.color)} />
                        </div>
                      )
                    })()}
                    <div className="text-xs text-gray-500">
                      {formatLastSync(details.last_sync)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sync Errors */}
        {tender.external_system_refs && Object.values(tender.external_system_refs).some(system => system.sync_errors && system.sync_errors.length > 0) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <AlertCircle className="w-4 h-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">Sync Issues</span>
            </div>
            
            <div className="space-y-1">
              {Object.entries(tender.external_system_refs).map(([system, details]) => 
                details.sync_errors && details.sync_errors.length > 0 && (
                  <div key={system} className="text-xs text-red-700">
                    <span className="font-medium capitalize">{system}:</span> {details.sync_errors[0]}
                  </div>
                )
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          <span className="text-xs text-gray-500">Integration Status</span>
          <div className="flex items-center space-x-2">
            <button className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800">
              <RefreshCw className="w-3 h-3" />
              <span>Sync All</span>
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
